import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Plus, Refresh<PERSON><PERSON>, Send, Spark<PERSON>, User } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useSearchParams } from "@remix-run/react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  createdAt: string;
  provider?: string;
  model?: string;
  tokenCount?: number;
  metadata?: any;
}

interface Conversation {
  id: string;
  title: string;
  model?: string;
  provider?: string;
  isArchived: boolean;
  lastMessageAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface AIChatInterfaceProps {
  className?: string;
  conversationId?: string;
}

export default function AIChatInterface({
  className,
  conversationId: propConversationId,
}: AIChatInterfaceProps) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("test message");
  const [isLoading, setIsLoading] = useState(false);
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [isLoadingConversation, setIsLoadingConversation] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Get conversation ID from props or URL params
  const conversationId = propConversationId || searchParams.get("conversation");

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load conversation when conversationId changes
  useEffect(() => {
    if (conversationId) {
      loadConversation(conversationId);
    } else {
      // Clear messages if no conversation selected
      setMessages([]);
      setCurrentConversation(null);
    }
  }, [conversationId]);

  // Load conversation and its messages
  const loadConversation = async (id: string) => {
    setIsLoadingConversation(true);
    try {
      const response = await fetch(`/api/chat/conversations/${id}`);
      if (!response.ok) {
        throw new Error("Failed to load conversation");
      }
      const data = await response.json();
      if (data.success) {
        setCurrentConversation(data.data.conversation);
        setMessages(data.data.messages || []);
      }
    } catch (error) {
      console.error("Failed to load conversation:", error);
    } finally {
      setIsLoadingConversation(false);
    }
  };

  // Create a new conversation
  const createNewConversation = async (firstMessage: string) => {
    try {
      const response = await fetch("/api/chat/conversations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "create",
          title: firstMessage.slice(0, 50) + (firstMessage.length > 50 ? "..." : ""),
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create conversation");
      }

      const data = await response.json();
      if (data.success) {
        const newConversation = data.data.conversation;
        setCurrentConversation(newConversation);
        // Update URL to include conversation ID
        setSearchParams({ conversation: newConversation.id });
        return newConversation.id;
      }
    } catch (error) {
      console.error("Failed to create conversation:", error);
    }
    return null;
  };

  // Save message to database
  const saveMessage = async (
    conversationId: string,
    message: Omit<Message, "id" | "createdAt">
  ) => {
    try {
      const response = await fetch("/api/chat/conversations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "add-message",
          conversationId,
          message,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to save message");
      }

      const data = await response.json();
      return data.success ? data.data.message : null;
    } catch (error) {
      console.error("Failed to save message:", error);
      return null;
    }
  };

  // 处理文本生成
  const handleTextGeneration = async (prompt: string, currentConvId?: string) => {
    try {
      // Try Cloudflare AI first, fallback to OpenAI
      let response = await fetch("/api/ai/cloudflare", {
        method: "POST",
        body: (() => {
          const formData = new FormData();
          formData.append("action", "generate-text");
          formData.append("model", "llama-3.2-3b");
          formData.append("prompt", prompt);
          formData.append("maxTokens", "2048");
          formData.append("temperature", "0.7");
          return formData;
        })(),
      });

      let result: any;
      let provider = "cloudflare";
      let model = "llama-3.2-3b";

      if (!response.ok) {
        // Fallback to OpenAI
        console.log("Cloudflare AI failed, trying OpenAI...");
        response = await fetch("/api/ai/generate-text", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            prompt,
            provider: "openai",
            model: "gpt-4o-mini",
          }),
        });
        provider = "openai";
        model = "gpt-4o-mini";
      }

      if (!response.ok) {
        throw new Error("Both AI providers failed");
      }

      result = await response.json();

      let content: string;
      if (provider === "cloudflare") {
        content = result.data?.result?.response || result.data?.result || "No response generated";
      } else {
        content = result.data?.text || "No response generated";
      }

      const assistantMessage: Message = {
        id: Date.now().toString() + "-assistant",
        role: "assistant",
        content,
        createdAt: new Date().toISOString(),
        provider,
        model,
      };

      setMessages((prev) => [...prev, assistantMessage]);

      // Save to database if we have a conversation
      if (currentConvId) {
        await saveMessage(currentConvId, {
          role: "assistant",
          content,
          provider,
          model,
        });
      }
    } catch (error) {
      console.error("Text generation error:", error);
      const errorMessage: Message = {
        id: Date.now().toString() + "-error",
        role: "assistant",
        content: "抱歉，生成文本时出现了错误。请稍后再试。",
        createdAt: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    }
  };

  // 提交消息
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("handleSubmit called", { input: input.trim(), isLoading });

    if (!input.trim() || isLoading) {
      console.log("Submit blocked:", { inputEmpty: !input.trim(), isLoading });
      return;
    }

    const messageContent = input.trim();
    setInput("");
    setIsLoading(true);

    try {
      let convId = currentConversation?.id;

      // Create new conversation if none exists
      if (!convId) {
        convId = await createNewConversation(messageContent);
        if (!convId) {
          throw new Error("Failed to create conversation");
        }
      }

      // Create user message
      const userMessage: Message = {
        id: Date.now().toString() + "-user",
        role: "user",
        content: messageContent,
        createdAt: new Date().toISOString(),
      };

      setMessages((prev) => [...prev, userMessage]);

      // Save user message to database
      await saveMessage(convId, {
        role: "user",
        content: messageContent,
      });

      // Generate AI response
      await handleTextGeneration(messageContent, convId);
    } catch (error) {
      console.error("Failed to send message:", error);
      // Add error message
      const errorMessage: Message = {
        id: Date.now().toString() + "-error",
        role: "assistant",
        content: "抱歉，发送消息时出现了错误。请稍后再试。",
        createdAt: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // 复制消息内容
  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  // 重新生成
  const regenerateMessage = async (prompt: string) => {
    if (isLoading || !currentConversation) return;

    setIsLoading(true);
    try {
      await handleTextGeneration(prompt, currentConversation.id);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`flex flex-col h-full bg-white dark:bg-gray-900 ${className}`}>
      {/* 消息区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-3xl mx-auto">
          {isLoadingConversation ? (
            <div className="flex flex-col items-center justify-center h-full py-8">
              <Loader2 className="w-8 h-8 animate-spin text-blue-500 mb-4" />
              <p className="text-gray-500 dark:text-gray-400">Loading conversation...</p>
            </div>
          ) : messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full py-8">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
                {currentConversation ? currentConversation.title : "Hello"}
              </h2>
              <p className="text-gray-500 dark:text-gray-400 text-center max-w-md mb-4">
                {currentConversation ? "Continue your conversation" : "What can I do for you?"}
              </p>
            </div>
          ) : (
            <div className="space-y-6 px-4 py-6">
              {messages.map((message) => (
                <div key={message.id} className="group">
                  <div className="flex gap-4">
                    <div className="w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center">
                      {message.role === "assistant" ? (
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                          <Sparkles className="w-4 h-4 text-white" />
                        </div>
                      ) : (
                        <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                          <User className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm text-gray-900 dark:text-white">
                          {message.role === "assistant" ? "ChatGPT" : "You"}
                        </span>
                        {message.provider && (
                          <Badge variant="secondary" className="text-xs">
                            {message.provider}
                          </Badge>
                        )}
                      </div>
                      <div className="prose prose-sm max-w-none dark:prose-invert">
                        <p className="whitespace-pre-wrap text-gray-900 dark:text-gray-100 leading-relaxed">
                          {message.content}
                        </p>
                      </div>
                      {message.role === "assistant" && (
                        <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyMessage(message.content)}
                            className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                          >
                            <Copy className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              const userMsg = messages[messages.indexOf(message) - 1];
                              if (userMsg) regenerateMessage(userMsg.content);
                            }}
                            className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                          >
                            <RefreshCw className="w-4 h-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {isLoading && (
            <div className="px-4 py-6">
              <div className="group">
                <div className="flex gap-4">
                  <div className="w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                      <Sparkles className="w-4 h-4 text-white" />
                    </div>
                  </div>
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm text-gray-900 dark:text-white">
                        ChatGPT
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]" />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]" />
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                      </div>
                      <span className="text-sm text-gray-500">Thinking...</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* 输入区域 */}
      <div className="bg-white dark:bg-gray-900">
        <div className="max-w-3xl mx-auto px-4 pb-4">
          <form onSubmit={handleSubmit}>
            <div className="relative flex items-end gap-2">
              <div className="flex-1 relative">
                <Textarea
                  ref={textareaRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e);
                    }
                  }}
                  placeholder="Message ChatGPT"
                  className="min-h-[78px] max-h-[200px] resize-none pr-12 text-base border-2 border-blue-200 dark:border-blue-700 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:border-blue-400 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 hover:border-blue-300 dark:hover:border-blue-600 relative z-0"
                  disabled={isLoading}
                  rows={3}
                />
                <button
                  type="submit"
                  disabled={!input.trim() || isLoading}
                  onClick={(e) => {
                    console.log("Button clicked", { input: input.trim(), isLoading, disabled: !input.trim() || isLoading });
                    if (!input.trim() || isLoading) {
                      e.preventDefault();
                      console.log("Button click prevented");
                    }
                  }}
                  className="absolute right-2 bottom-2 h-8 w-8 p-0 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-10 pointer-events-auto flex items-center justify-center border-0 cursor-pointer disabled:cursor-not-allowed"
                >
                  <Send className="w-4 h-4 text-white" />
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
