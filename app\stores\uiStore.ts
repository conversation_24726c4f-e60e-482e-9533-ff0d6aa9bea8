import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import type { Notification, UIState } from "./types";

// 应用主题到 document 的函数
const applyThemeToDocument = (theme: "light" | "dark" | "system") => {
  if (typeof window === "undefined") return;

  console.log("UIStore: Applying theme to document:", theme);
  const root = document.documentElement;

  // 移除所有主题类
  root.classList.remove("light", "dark");

  if (theme === "system") {
    // 系统主题：根据用户系统偏好设置
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const isDark = mediaQuery.matches;
    root.classList.add(isDark ? "dark" : "light");
    console.log("UIStore: Applied system theme:", isDark ? "dark" : "light");

    // 监听系统主题变化
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      root.classList.remove("light", "dark");
      root.classList.add(e.matches ? "dark" : "light");
    };

    // 清理之前的监听器
    mediaQuery.removeEventListener("change", handleSystemThemeChange);
    mediaQuery.addEventListener("change", handleSystemThemeChange);
  } else {
    // 手动设置的主题
    root.classList.add(theme);
    console.log("UIStore: Applied manual theme:", theme);
  }
};

const initialState = {
  theme: "system" as const,

  sidebarOpen: false,
  notifications: [],
};

export const useUIStore = create<UIState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        setTheme: (theme) => {
          console.log("UIStore: Setting theme to:", theme);
          set({ theme }, false, "setTheme");

          // 应用主题到 document
          if (typeof window !== "undefined") {
            applyThemeToDocument(theme);
          }
        },

        toggleSidebar: () => {
          set((state) => ({ sidebarOpen: !state.sidebarOpen }), false, "toggleSidebar");
        },

        setSidebarOpen: (open) => {
          set({ sidebarOpen: open }, false, "setSidebarOpen");
        },

        addNotification: (notification) => {
          const newNotification: Notification = {
            ...notification,
            id: crypto.randomUUID(),
          };
          set(
            (state) => ({
              notifications: [...state.notifications, newNotification],
            }),
            false,
            "addNotification"
          );

          // Auto-remove notification if duration is set
          if (notification.duration && notification.duration > 0) {
            setTimeout(() => {
              get().removeNotification(newNotification.id);
            }, notification.duration);
          }
        },

        removeNotification: (id) => {
          set(
            (state) => ({
              notifications: state.notifications.filter((n) => n.id !== id),
            }),
            false,
            "removeNotification"
          );
        },

        clearNotifications: () => {
          set({ notifications: [] }, false, "clearNotifications");
        },

        reset: () => {
          set(initialState, false, "reset");
        },
      }),
      {
        name: "ui-store",
        partialize: (state) => ({
          theme: state.theme,
          sidebarOpen: state.sidebarOpen,
        }),
        skipHydration: typeof window === "undefined",
      }
    ),
    {
      name: "ui-store",
    }
  )
);

// Selector functions
export const selectTheme = (state: UIState) => state.theme;

export const selectSidebarOpen = (state: UIState) => state.sidebarOpen;
export const selectNotifications = (state: UIState) => state.notifications;

// Convenience hooks
export const useTheme = () => useUIStore(selectTheme);

export const useSidebarOpen = () => useUIStore(selectSidebarOpen);
export const useNotifications = () => useUIStore(selectNotifications);

// Actions hooks
export const useUIActions = () => {
  const setTheme = useUIStore((state) => state.setTheme);

  const toggleSidebar = useUIStore((state) => state.toggleSidebar);
  const setSidebarOpen = useUIStore((state) => state.setSidebarOpen);
  const addNotification = useUIStore((state) => state.addNotification);
  const removeNotification = useUIStore((state) => state.removeNotification);
  const clearNotifications = useUIStore((state) => state.clearNotifications);
  const reset = useUIStore((state) => state.reset);

  return {
    setTheme,

    toggleSidebar,
    setSidebarOpen,
    addNotification,
    removeNotification,
    clearNotifications,
    reset,
  };
};
