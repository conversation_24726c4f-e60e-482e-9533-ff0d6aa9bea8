import { Link, useLocation } from "@remix-run/react";
import { ChevronDown, ChevronRight, Settings, Sparkles, X } from "lucide-react";
import { type ReactNode, useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { defaultSidebarConfig } from "~/config/sidebar.config";
import { cn } from "~/lib/utils/utils";

export interface SidebarItem {
  title: string;
  url: string;
  icon: ReactNode;
  badge?: string;
  children?: SidebarItem[];
  description?: string;
}

export interface SidebarProps {
  items?: SidebarItem[];
  className?: string;
  onClose?: () => void;
  showCloseButton?: boolean;
  title?: string;
  logo?: ReactNode;
}

export default function Sidebar({
  items = defaultSidebarConfig,
  className = "",
  onClose,
  showCloseButton = false,
  title = "Navigation",
  logo,
}: SidebarProps) {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const location = useLocation();

  const isActive = (url: string) => {
    if (url === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(url);
  };

  const toggleExpanded = (title: string) => {
    setExpandedItems((prev) =>
      prev.includes(title) ? prev.filter((item) => item !== title) : [...prev, title]
    );
  };

  const renderSidebarItem = (item: SidebarItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.title);
    const active = isActive(item.url);

    return (
      <div key={item.title} className="w-full">
        <div
          className={cn(
            "flex items-center justify-between w-full rounded-lg transition-all duration-200 group",
            level === 0 ? "px-3 py-2.5" : "px-6 py-2",
            active
              ? "bg-primary/10 text-primary border-r-2 border-primary"
              : "hover:bg-muted/50 text-muted-foreground hover:text-foreground"
          )}
        >
          <Link to={item.url} className="flex items-center gap-3 flex-1 min-w-0" onClick={onClose}>
            <div
              className={cn(
                "flex-shrink-0 transition-colors",
                active ? "text-primary" : "text-muted-foreground group-hover:text-foreground"
              )}
            >
              {item.icon}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <span
                  className={cn(
                    "font-medium truncate",
                    level === 0 ? "text-sm" : "text-xs",
                    active ? "text-primary" : ""
                  )}
                >
                  {item.title}
                </span>
                {item.badge && (
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                    {item.badge}
                  </Badge>
                )}
              </div>
              {item.description && level === 0 && (
                <p className="text-xs text-muted-foreground truncate mt-0.5">{item.description}</p>
              )}
            </div>
          </Link>
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 flex-shrink-0"
              onClick={() => toggleExpanded(item.title)}
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map((child) => renderSidebarItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("flex flex-col h-full bg-background border-r", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          {logo || (
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Sparkles className="h-4 w-4 text-white" />
            </div>
          )}
          <span className="font-bold text-lg">{title}</span>
        </div>
        {showCloseButton && onClose && (
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-5 w-5" />
          </Button>
        )}
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 overflow-y-auto p-4">
        <div className="space-y-2">{items.map((item) => renderSidebarItem(item))}</div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t">
        <Link
          to="/settings"
          className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-muted/50 text-muted-foreground hover:text-foreground transition-colors"
          onClick={onClose}
        >
          <Settings className="h-5 w-5" />
          <span className="font-medium text-sm">Settings</span>
        </Link>
      </div>
    </div>
  );
}
