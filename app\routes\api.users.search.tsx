import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { createDbFromEnv } from "~/lib/db";
import { searchUsers } from "~/models/user";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Create database connection
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    // Parse search parameters
    const url = new URL(request.url);
    const searchParams = {
      search: url.searchParams.get("search") || undefined,
      page: parseInt(url.searchParams.get("page") || "1"),
      limit: parseInt(url.searchParams.get("limit") || "10"),
      sortBy: url.searchParams.get("sortBy") || "createdAt",
      sortOrder: (url.searchParams.get("sortOrder") as "asc" | "desc") || "desc",
      isAffiliate:
        url.searchParams.get("isAffiliate") === "true"
          ? true
          : url.searchParams.get("isAffiliate") === "false"
            ? false
            : undefined,
      minCredits: url.searchParams.get("minCredits")
        ? parseInt(url.searchParams.get("minCredits")!)
        : undefined,
      maxCredits: url.searchParams.get("maxCredits")
        ? parseInt(url.searchParams.get("maxCredits")!)
        : undefined,
    };

    // Validate pagination parameters
    if (searchParams.page < 1) searchParams.page = 1;
    if (searchParams.limit < 1 || searchParams.limit > 100) searchParams.limit = 10;

    // Search users with the new enhanced search function
    const result = await searchUsers(db, searchParams);

    return json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error("Error searching users:", error);
    return json(
      {
        success: false,
        error: "Failed to search users",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST method for more complex search queries
export async function action({ request, context }: LoaderFunctionArgs) {
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    // Parse request body
    const body = await request.json();
    const searchParams = {
      search: body.search || undefined,
      page: body.page || 1,
      limit: body.limit || 10,
      sortBy: body.sortBy || "createdAt",
      sortOrder: body.sortOrder || "desc",
      isAffiliate: body.isAffiliate,
      minCredits: body.minCredits,
      maxCredits: body.maxCredits,
      // Additional filters from request body
      email: body.email,
      name: body.name,
    };

    // Validate pagination parameters
    if (searchParams.page < 1) searchParams.page = 1;
    if (searchParams.limit < 1 || searchParams.limit > 100) searchParams.limit = 10;

    // Search users
    const result = await searchUsers(db, searchParams);

    return json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error("Error searching users:", error);
    return json(
      {
        success: false,
        error: "Failed to search users",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
