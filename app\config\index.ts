// Centralized configuration exports
// This file provides a single entry point for all configuration

// Export specific items to avoid naming conflicts
export {
  siteConfig,
  footerLinks,
  getAnalyticsProps,
  isExternalUrl,
  type FooterLinkSection,
  type FooterLink,
  type SocialPlatform,
} from "./footer.config";

export {
  designSystem,
  colors,
  typography,
  spacing as designSpacing,
  borderRadius,
  shadows,
  animations,
  componentVariants,
  layouts,
  uxPatterns,
  utils,
  breakpoints as designBreakpoints,
} from "./design-system";

export {
  default as uiConfig,
  themeConfig,
  componentDefaults,
  animationConfig,
  breakpoints as uiBreakpoints,
  zIndex,
  spacing as uiSpacing,
} from "./ui";

export { CacheKey, Theme, type ThemeType } from "./constants";

export {
  siteConfig as seoSiteConfig,
  pageConfigs,
  getPageConfig,
  getCanonicalUrl,
  getWebsiteStructuredData,
  getOrganizationStructuredData,
} from "./seo";

export {
  getLandingPageConfig,
  type LandingPageConfig,
} from "./landing";

// Re-export commonly used configs with aliases for convenience
export { siteConfig as site } from "./footer.config";
export { designSystem as design } from "./design-system";

// Environment-specific configurations
export const env = {
  isDevelopment: process.env.NODE_ENV === "development",
  isProduction: process.env.NODE_ENV === "production",
  isTest: process.env.NODE_ENV === "test",
} as const;

// API endpoints configuration
export const apiConfig = {
  baseUrl: process.env.API_BASE_URL || "/api",
  timeout: 10000,
  retries: 3,
} as const;

// Feature flags that can be controlled via environment variables
export const featureFlags = {
  enableAnalytics: process.env.ENABLE_ANALYTICS === "true",
  enableNewsletter: process.env.ENABLE_NEWSLETTER === "true",
  enableBlog: process.env.ENABLE_BLOG !== "false", // Default to true
  enableResources: process.env.ENABLE_RESOURCES === "true",
  enableStatus: process.env.ENABLE_STATUS === "true",
} as const;
