import type { MetaFunction } from "@remix-run/cloudflare";
import { <PERSON> } from "@remix-run/react";
import {
  <PERSON>R<PERSON>,
  Check,
  Code,
  Globe,
  Heart,
  Lightbulb,
  Rocket,
  Sparkles,
  Target,
  Users,
  Zap,
} from "lucide-react";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export const meta: MetaFunction = () => {
  return [
    { title: "About Us - AI SaaS Starter" },
    {
      name: "description",
      content:
        "Learn about our mission to democratize AI technology and make it accessible to developers and businesses worldwide.",
    },
  ];
};

const values = [
  {
    icon: <Target className="h-6 w-6" />,
    title: "Mission-Driven",
    description:
      "We're committed to making AI technology accessible to everyone, from individual developers to enterprise teams.",
  },
  {
    icon: <Users className="h-6 w-6" />,
    title: "Community First",
    description:
      "Our success is measured by the success of our community. We build tools that empower creators and innovators.",
  },
  {
    icon: <Lightbulb className="h-6 w-6" />,
    title: "Innovation",
    description:
      "We stay at the forefront of AI technology, constantly exploring new possibilities and pushing boundaries.",
  },
  {
    icon: <Heart className="h-6 w-6" />,
    title: "Quality Focus",
    description:
      "Every feature we build is crafted with care, tested thoroughly, and designed to provide exceptional user experience.",
  },
];

const features = [
  {
    icon: <Code className="h-6 w-6" />,
    title: "Developer-First",
    description:
      "Built by developers, for developers. Clean APIs, comprehensive docs, and powerful tools.",
  },
  {
    icon: <Zap className="h-6 w-6" />,
    title: "Lightning Fast",
    description: "Optimized for performance with edge computing and global CDN distribution.",
  },
  {
    icon: <Globe className="h-6 w-6" />,
    title: "Global Scale",
    description: "Serving users worldwide with 99.9% uptime and enterprise-grade reliability.",
  },
];

const stats = [
  { label: "Active Users", value: "10,000+" },
  { label: "API Requests", value: "1M+" },
  { label: "Countries", value: "50+" },
  { label: "Uptime", value: "99.9%" },
];

const team = [
  {
    name: "Alex Chen",
    role: "Founder & CEO",
    bio: "Former AI researcher at Google, passionate about democratizing AI technology.",
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
  },
  {
    name: "Sarah Johnson",
    role: "CTO",
    bio: "Full-stack engineer with 10+ years experience building scalable platforms.",
    avatar:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
  },
  {
    name: "Michael Rodriguez",
    role: "Head of Product",
    bio: "Product strategist focused on creating intuitive developer experiences.",
    avatar:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
  },
];

export default function AboutPage() {
  return (
    <UnifiedLayout
      headerProps={{
        showThemeToggle: true,
        showLanguageSwitch: true,
      }}
      hero={{
        badge: {
          text: "🚀 About Our Mission",
        },
        title: "Democratizing AI for Everyone",
        description:
          "We believe AI should be accessible, powerful, and easy to use. Our platform empowers developers and businesses to harness the full potential of artificial intelligence without the complexity.",
        buttons: [
          {
            text: "Try Our Platform",
            href: "/ai-tools",
            variant: "primary",
            icon: <ArrowRight className="h-5 w-5" />,
          },
          {
            text: "Contact Us",
            href: "/contact",
            variant: "outline",
          },
        ],
        trustIndicators: ["Trusted by 10,000+ developers", "99.9% uptime SLA", "Enterprise ready"],
        backgroundPattern: "gradient",
      }}
    >
      {/* Enhanced Stats Section */}
      <section className="py-24 bg-gradient-primary relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-primary/10 to-purple-600/10 rounded-full blur-3xl animate-pulse" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="relative">
                  <div className="text-4xl lg:text-5xl font-extrabold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                    {stat.value}
                  </div>
                  <div className="absolute -inset-2 bg-gradient-to-r from-primary/20 to-purple-600/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
                </div>
                <div className="text-sm font-medium text-muted-foreground uppercase tracking-wider">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-24 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-headline font-bold text-foreground mb-4">Our Values</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              These core principles guide everything we do and shape how we build our platform.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <Card
                key={index}
                className="card-modern p-6 hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                <CardHeader>
                  <div className="w-16 h-16 btn-primary-gradient rounded-xl flex items-center justify-center text-white mx-auto mb-4">
                    {value.icon}
                  </div>
                  <CardTitle className="text-xl text-center">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base text-center">
                    {value.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-headline font-bold text-foreground mb-4">Why Choose Us</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We've built our platform with the needs of modern developers and businesses in mind.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="card-modern text-center hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                <CardHeader>
                  <div className="w-16 h-16 btn-primary-gradient rounded-xl flex items-center justify-center text-white mx-auto mb-4">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-24 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-headline font-bold text-foreground mb-4">Meet Our Team</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We're a passionate team of engineers, designers, and AI enthusiasts working to make AI
              accessible to everyone.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <Card
                key={index}
                className="card-modern text-center hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                <CardHeader>
                  <div className="relative">
                    <img
                      src={member.avatar}
                      alt={member.name}
                      className="w-24 h-24 rounded-full mx-auto mb-4 object-cover ring-4 ring-primary/20"
                    />
                    <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
                      <div className="w-6 h-6 btn-primary-gradient rounded-full flex items-center justify-center">
                        <Check className="h-3 w-3 text-white" />
                      </div>
                    </div>
                  </div>
                  <CardTitle className="text-xl">{member.name}</CardTitle>
                  <Badge
                    variant="secondary"
                    className="bg-gradient-primary text-primary-foreground"
                  >
                    {member.role}
                  </Badge>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{member.bio}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section className="py-32 bg-gradient-to-b from-background via-muted/20 to-background relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-gradient-to-r from-blue-400/15 to-purple-400/15 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-1/4 right-1/4 w-[500px] h-[500px] bg-gradient-to-l from-purple-400/15 to-pink-400/15 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="absolute inset-0 bg-grid-pattern opacity-[0.02]" />
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <h2 className="text-5xl font-extrabold lg:text-6xl bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent leading-tight mb-8">
                Ready to Get Started?
              </h2>
              <div className="absolute -inset-4 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-cyan-600/10 blur-2xl -z-10 animate-pulse" />
            </div>

            <p className="text-xl text-gray-600 dark:text-gray-300 mb-12 leading-relaxed font-medium">
              Join thousands of developers who are already building amazing AI-powered applications
              with our platform.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <div className="group relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-1000 group-hover:duration-200" />
                <Button
                  size="lg"
                  asChild
                  className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 hover:from-blue-700 hover:via-purple-700 hover:to-cyan-700 text-white border-0 shadow-2xl hover:shadow-blue-500/40 px-8 py-4 text-lg font-bold rounded-2xl"
                >
                  <Link to="/ai-tools" className="flex items-center gap-3">
                    Start Building
                    <Rocket className="h-5 w-5 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
              </div>

              <Button
                size="lg"
                variant="outline"
                asChild
                className="border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 shadow-xl hover:shadow-2xl px-8 py-4 text-lg font-bold rounded-2xl"
              >
                <Link to="/pricing">View Pricing</Link>
              </Button>
            </div>

            {/* Trust indicators */}
            <div className="mt-12 flex items-center justify-center gap-8 text-sm text-muted-foreground flex-wrap">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                <span>Trusted by 10,000+ developers</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse delay-300" />
                <span>99.9% uptime SLA</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse delay-600" />
                <span>Enterprise ready</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </UnifiedLayout>
  );
}
