import CardGrid from "./card-grid";
import ContentSection from "./content-section";

interface ShowcaseItem {
  title: string;
  description: string;
  image: string;
  url?: string;
}

interface ShowcaseProps {
  title: string;
  description: string;
  items: ShowcaseItem[];
}

export default function Showcase({ title, description, items }: ShowcaseProps) {
  return (
    <ContentSection
      title={title}
      description={description}
      background="showcase"
      decorations={true}
      padding="lg"
      headerSpacing="lg"
    >
      <CardGrid items={items} columns={4} variant="showcase" animationDelay={0.15} />
    </ContentSection>
  );
}
