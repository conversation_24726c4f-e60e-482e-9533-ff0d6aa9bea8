import CardGrid from "./card-grid";
import ContentSection from "./content-section";

interface ShowcaseItem {
  title: string;
  description: string;
  image: string;
  url?: string;
}

interface ShowcaseProps {
  title: string;
  description: string;
  items: ShowcaseItem[];
}

export default function Showcase({ title, description, items }: ShowcaseProps) {
  return (
    <ContentSection
      title={title}
      description={description}
      background="showcase"
      decorations={true}
      padding="lg"
      headerSpacing="lg"
    >
      {/* Decorative line */}
      <div className="flex items-center justify-center gap-4 mb-16">
        <div className="w-12 h-0.5 bg-gradient-to-r from-transparent to-blue-500" />
        <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse" />
        <div className="w-12 h-0.5 bg-gradient-to-r from-purple-500 to-transparent" />
      </div>

      <CardGrid items={items} columns={3} variant="showcase" animationDelay={0.15} />
    </ContentSection>
  );
}
