// app/components/theme/theme-initializer.tsx
"use client";

import { useEffect } from "react";
import { useAppStore } from "~/stores/appStore";
import { useUIStore } from "~/stores/uiStore";

export function ThemeInitializer() {
  const theme = useUIStore((state) => state.theme);
  const isInitialized = useAppStore((state) => state.isInitialized);

  useEffect(() => {
    // 等待 zustand 水合完成
    if (!isInitialized) {
      console.log("ThemeInitializer: Waiting for zustand hydration...");
      return;
    }

    console.log("ThemeInitializer: Zustand hydrated, current theme:", theme);

    if (typeof window === "undefined") return;

    const root = document.documentElement;

    // 移除所有主题类
    root.classList.remove("light", "dark");

    if (theme === "system") {
      // 系统主题：根据用户系统偏好设置
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const isDark = mediaQuery.matches;
      root.classList.add(isDark ? "dark" : "light");
      console.log("ThemeInitializer: Applied system theme:", isDark ? "dark" : "light");

      // 监听系统主题变化
      const handleChange = (e: MediaQueryListEvent) => {
        root.classList.remove("light", "dark");
        root.classList.add(e.matches ? "dark" : "light");
        console.log("ThemeInitializer: System theme changed to:", e.matches ? "dark" : "light");
      };

      mediaQuery.addEventListener("change", handleChange);

      return () => {
        mediaQuery.removeEventListener("change", handleChange);
      };
    } else {
      // 手动设置的主题
      root.classList.add(theme);
      console.log("ThemeInitializer: Applied manual theme:", theme);
    }
  }, [theme, isInitialized]);

  // 这个组件不渲染任何内容，只是用来初始化主题
  return null;
}
